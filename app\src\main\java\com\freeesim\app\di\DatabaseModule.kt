package com.freeesim.app.di

import android.content.Context
import androidx.room.Room
import com.freeesim.app.data.database.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideDatabase(@ApplicationContext context: Context): FreeESIMDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            FreeESIMDatabase::class.java,
            "free_esim_database"
        ).build()
    }
    
    @Provides
    fun provideUserDao(database: FreeESIMDatabase): UserDao {
        return database.userDao()
    }
    
    @Provides
    fun provideDataUsageDao(database: FreeESIMDatabase): DataUsageDao {
        return database.dataUsageDao()
    }
    
    @Provides
    fun provideAdRewardDao(database: FreeESIMDatabase): AdRewardDao {
        return database.adRewardDao()
    }
    
    @Provides
    fun provideESIMProfileDao(database: FreeESIMDatabase): ESIMProfileDao {
        return database.esimProfileDao()
    }
}
