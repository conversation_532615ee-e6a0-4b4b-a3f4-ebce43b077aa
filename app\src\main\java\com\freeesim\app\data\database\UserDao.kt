package com.freeesim.app.data.database

import androidx.room.*
import com.freeesim.app.data.model.User
import com.freeesim.app.data.model.DataUsage
import com.freeesim.app.data.model.AdReward
import com.freeesim.app.data.model.ESIMProfile
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {
    @Query("SELECT * FROM users WHERE id = :userId")
    fun getUser(userId: String): Flow<User?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: User)
    
    @Update
    suspend fun updateUser(user: User)
    
    @Query("UPDATE users SET dataCredits = dataCredits + :credits WHERE id = :userId")
    suspend fun addDataCredits(userId: String, credits: Long)
    
    @Query("UPDATE users SET dataCredits = dataCredits - :usage, totalDataUsed = totalDataUsed + :usage WHERE id = :userId")
    suspend fun deductDataUsage(userId: String, usage: Long)
}

@Dao
interface DataUsageDao {
    @Query("SELECT * FROM data_usage WHERE userId = :userId ORDER BY timestamp DESC")
    fun getUserDataUsage(userId: String): Flow<List<DataUsage>>
    
    @Insert
    suspend fun insertDataUsage(dataUsage: DataUsage)
    
    @Query("SELECT SUM(dataUsedMB) FROM data_usage WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getDataUsageInPeriod(userId: String, startTime: Long): Long?
}

@Dao
interface AdRewardDao {
    @Query("SELECT * FROM ad_rewards WHERE userId = :userId ORDER BY timestamp DESC")
    fun getUserAdRewards(userId: String): Flow<List<AdReward>>
    
    @Insert
    suspend fun insertAdReward(adReward: AdReward)
    
    @Query("UPDATE ad_rewards SET isRewardClaimed = 1 WHERE id = :rewardId")
    suspend fun claimReward(rewardId: String)
    
    @Query("SELECT COUNT(*) FROM ad_rewards WHERE userId = :userId AND timestamp >= :startTime")
    suspend fun getAdsWatchedInPeriod(userId: String, startTime: Long): Int
}

@Dao
interface ESIMProfileDao {
    @Query("SELECT * FROM esim_profiles WHERE userId = :userId")
    fun getUserESIMProfiles(userId: String): Flow<List<ESIMProfile>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertESIMProfile(profile: ESIMProfile)
    
    @Update
    suspend fun updateESIMProfile(profile: ESIMProfile)
    
    @Query("SELECT * FROM esim_profiles WHERE iccid = :iccid")
    suspend fun getESIMProfile(iccid: String): ESIMProfile?
}
