package com.freeesim.app.data.api

import retrofit2.Response
import retrofit2.http.*

/**
 * iBASIS eSIM API implementation
 * Free trial available at: https://ibasis.com/innovation/
 */
interface IBasisESIMApi {
    
    @POST("v1/esims")
    suspend fun createESIM(
        @Header("Authorization") authorization: String,
        @Body request: IBasisCreateRequest
    ): Response<IBasisCreateResponse>
    
    @GET("v1/esims/{esim_id}")
    suspend fun getESIM(
        @Header("Authorization") authorization: String,
        @Path("esim_id") esimId: String
    ): Response<IBasisESIMResponse>
    
    @POST("v1/esims/{esim_id}/activate")
    suspend fun activateESIM(
        @Header("Authorization") authorization: String,
        @Path("esim_id") esimId: String
    ): Response<IBasisActivateResponse>
    
    @GET("v1/esims/{esim_id}/usage")
    suspend fun getUsage(
        @Header("Authorization") authorization: String,
        @Path("esim_id") esimId: String
    ): Response<IBasisUsageResponse>
}

// iBASIS-specific request/response models
data class IBasisCreateRequest(
    val country_code: String? = null,
    val data_limit_mb: Long,
    val plan_type: String = "data_only"
)

data class IBasisCreateResponse(
    val esim_id: String,
    val iccid: String,
    val activation_code: String,
    val qr_code: String,
    val status: String,
    val data_limit_mb: Long,
    val created_at: String
)

data class IBasisESIMResponse(
    val esim_id: String,
    val iccid: String,
    val status: String,
    val data_limit_mb: Long,
    val data_used_mb: Long,
    val created_at: String,
    val activated_at: String?
)

data class IBasisActivateResponse(
    val success: Boolean,
    val message: String,
    val activated_at: String?
)

data class IBasisUsageResponse(
    val esim_id: String,
    val total_usage_mb: Long,
    val usage_records: List<IBasisUsageRecord>
)

data class IBasisUsageRecord(
    val timestamp: String,
    val data_mb: Long,
    val country: String,
    val network: String
)

/**
 * iBASIS eSIM Provider Implementation
 * Free trial available - perfect for testing!
 */
class IBasisESIMProvider(
    private val api: IBasisESIMApi,
    private val apiKey: String
) : ESIMProviderInterface {
    
    private val authHeader = "Bearer $apiKey"
    
    override suspend fun purchaseESIM(request: PurchaseESIMRequest): Result<PurchaseESIMResponse> {
        return try {
            val ibasisRequest = IBasisCreateRequest(
                country_code = request.country,
                data_limit_mb = request.dataLimitMB
            )
            
            val response = api.createESIM(authHeader, ibasisRequest)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!
                Result.success(
                    PurchaseESIMResponse(
                        esimId = data.esim_id,
                        iccid = data.iccid,
                        activationCode = data.activation_code,
                        qrCode = data.qr_code,
                        status = data.status,
                        dataLimitMB = data.data_limit_mb,
                        validUntil = "", // iBASIS doesn't provide expiry in create response
                        cost = 0.0, // Free trial
                        currency = "USD"
                    )
                )
            } else {
                Result.failure(Exception("Failed to create eSIM: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getESIMDetails(esimId: String): Result<ESIMDetailsResponse> {
        return try {
            val response = api.getESIM(authHeader, esimId)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!
                Result.success(
                    ESIMDetailsResponse(
                        esimId = data.esim_id,
                        iccid = data.iccid,
                        status = data.status,
                        dataLimitMB = data.data_limit_mb,
                        dataUsedMB = data.data_used_mb,
                        dataRemainingMB = data.data_limit_mb - data.data_used_mb,
                        validUntil = "", // Would need additional API call
                        activatedAt = data.activated_at,
                        networks = emptyList() // Would need additional API call
                    )
                )
            } else {
                Result.failure(Exception("Failed to get eSIM details: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun activateESIM(esimId: String, request: ActivateESIMRequest): Result<ActivateESIMResponse> {
        return try {
            val response = api.activateESIM(authHeader, esimId)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!
                Result.success(
                    ActivateESIMResponse(
                        success = data.success,
                        message = data.message,
                        activatedAt = data.activated_at
                    )
                )
            } else {
                Result.failure(Exception("Failed to activate eSIM: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getDataUsage(esimId: String): Result<DataUsageResponse> {
        return try {
            val response = api.getUsage(authHeader, esimId)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!
                val usageRecords = data.usage_records.map { record ->
                    UsageRecord(
                        timestamp = record.timestamp,
                        dataMB = record.data_mb,
                        country = record.country,
                        network = record.network
                    )
                }
                
                Result.success(
                    DataUsageResponse(
                        esimId = esimId,
                        totalDataMB = 0, // Would get from eSIM details
                        usedDataMB = data.total_usage_mb,
                        remainingDataMB = 0, // Would calculate from total - used
                        usageHistory = usageRecords
                    )
                )
            } else {
                Result.failure(Exception("Failed to get usage data: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun addDataCredits(esimId: String, request: TopUpRequest): Result<TopUpResponse> {
        // iBASIS may not support direct top-up, would need to purchase additional eSIM
        return Result.failure(Exception("Top-up not supported by iBASIS API"))
    }
}
