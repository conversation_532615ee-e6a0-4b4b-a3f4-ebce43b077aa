package com.freeesim.app.data.api

import retrofit2.Response
import retrofit2.http.*

/**
 * Generic eSIM Provider API interface
 * Supports multiple providers: Telnyx, Twilio, Soracom, etc.
 */
interface ESIMProviderApi {
    
    @POST("esims")
    suspend fun purchaseESIM(
        @Body request: PurchaseESIMRequest
    ): Response<PurchaseESIMResponse>
    
    @GET("esims/{esim_id}")
    suspend fun getESIMDetails(
        @Path("esim_id") esimId: String
    ): Response<ESIMDetailsResponse>
    
    @POST("esims/{esim_id}/activate")
    suspend fun activateESIM(
        @Path("esim_id") esimId: String,
        @Body request: ActivateESIMRequest
    ): Response<ActivateESIMResponse>
    
    @GET("esims/{esim_id}/usage")
    suspend fun getDataUsage(
        @Path("esim_id") esimId: String
    ): Response<DataUsageResponse>
    
    @POST("esims/{esim_id}/top-up")
    suspend fun addDataCredits(
        @Path("esim_id") esimId: String,
        @Body request: TopUpRequest
    ): Response<TopUpResponse>
}

// Request/Response models for eSIM operations
data class PurchaseESIMRequest(
    val country: String? = null,
    val region: String? = null,
    val dataLimitMB: Long,
    val validityDays: Int = 30
)

data class PurchaseESIMResponse(
    val esimId: String,
    val iccid: String,
    val activationCode: String,
    val qrCode: String,
    val status: String,
    val dataLimitMB: Long,
    val validUntil: String,
    val cost: Double,
    val currency: String
)

data class ESIMDetailsResponse(
    val esimId: String,
    val iccid: String,
    val status: String,
    val dataLimitMB: Long,
    val dataUsedMB: Long,
    val dataRemainingMB: Long,
    val validUntil: String,
    val activatedAt: String?,
    val networks: List<NetworkInfo>
)

data class NetworkInfo(
    val country: String,
    val countryCode: String,
    val networkName: String,
    val networkCode: String
)

data class ActivateESIMRequest(
    val deviceId: String? = null
)

data class ActivateESIMResponse(
    val success: Boolean,
    val message: String,
    val activatedAt: String?
)

data class DataUsageResponse(
    val esimId: String,
    val totalDataMB: Long,
    val usedDataMB: Long,
    val remainingDataMB: Long,
    val usageHistory: List<UsageRecord>
)

data class UsageRecord(
    val timestamp: String,
    val dataMB: Long,
    val country: String,
    val network: String
)

data class TopUpRequest(
    val dataMB: Long
)

data class TopUpResponse(
    val success: Boolean,
    val newDataLimitMB: Long,
    val cost: Double,
    val currency: String
)
