package com.freeesim.app.di

import com.freeesim.app.data.api.*
import com.freeesim.app.config.ESIMProviderConfig
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import javax.inject.Singleton
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .build()
    }
    
    @Provides
    @Singleton
    @Named("telnyx")
    fun provideTelnyxRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://api.telnyx.com/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    @Provides
    @Singleton
    fun provideTelnyxESIMApi(@Named("telnyx") retrofit: Retrofit): TelnyxESIMApi {
        return retrofit.create(TelnyxESIMApi::class.java)
    }
    
    @Provides
    @Singleton
    fun provideESIMProvider(
        telnyxApi: TelnyxESIMApi
    ): ESIMProviderInterface {
        return when (ESIMProviderConfig.ACTIVE_PROVIDER) {
            ESIMProviderConfig.Provider.TELNYX -> {
                // In production, you would get this from BuildConfig or environment variables
                val apiKey = "YOUR_TELNYX_API_KEY_HERE"
                TelnyxESIMProvider(telnyxApi, apiKey)
            }
            ESIMProviderConfig.Provider.DEMO -> {
                DemoESIMProvider()
            }
            else -> {
                // Default to demo for now
                DemoESIMProvider()
            }
        }
    }
}
