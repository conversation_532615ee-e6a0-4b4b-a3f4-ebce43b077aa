package com.freeesim.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "users")
data class User(
    @PrimaryKey
    val id: String,
    val email: String,
    val phoneNumber: String? = null,
    val dataCredits: Long = 0, // in MB
    val totalDataEarned: Long = 0, // in MB
    val totalDataUsed: Long = 0, // in MB
    val isESIMActivated: Boolean = false,
    val esimIccid: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val lastActiveAt: Long = System.currentTimeMillis()
)
