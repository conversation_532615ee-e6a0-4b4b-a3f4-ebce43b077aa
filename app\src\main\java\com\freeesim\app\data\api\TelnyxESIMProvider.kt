package com.freeesim.app.data.api

import retrofit2.Response
import retrofit2.http.*

/**
 * Telnyx eSIM API implementation
 * Documentation: https://developers.telnyx.com/api/wireless/purchase-e-sim
 */
interface TelnyxESIMApi {
    
    @POST("v2/esims")
    suspend fun purchaseESIM(
        @Header("Authorization") authorization: String,
        @Body request: TelnyxPurchaseRequest
    ): Response<TelnyxPurchaseResponse>
    
    @GET("v2/esims/{id}")
    suspend fun getESIM(
        @Header("Authorization") authorization: String,
        @Path("id") esimId: String
    ): Response<TelnyxESIMResponse>
    
    @PATCH("v2/esims/{id}/actions/enable")
    suspend fun enableESIM(
        @Header("Authorization") authorization: String,
        @Path("id") esimId: String
    ): Response<TelnyxActionResponse>
    
    @GET("v2/esims/{id}/usage")
    suspend fun getUsage(
        @Header("Authorization") authorization: String,
        @Path("id") esimId: String
    ): Response<TelnyxUsageResponse>
}

// Telnyx-specific request/response models
data class TelnyxPurchaseRequest(
    val country_code: String? = null,
    val data_limit_mb: Long,
    val plan_type: String = "data_only" // or "voice_and_data"
)

data class TelnyxPurchaseResponse(
    val data: TelnyxESIMData
)

data class TelnyxESIMData(
    val id: String,
    val iccid: String,
    val msisdn: String?,
    val status: String,
    val data_limit_mb: Long,
    val data_used_mb: Long,
    val activation_code: String,
    val qr_code_url: String,
    val created_at: String,
    val updated_at: String
)

data class TelnyxESIMResponse(
    val data: TelnyxESIMData
)

data class TelnyxActionResponse(
    val data: TelnyxESIMData
)

data class TelnyxUsageResponse(
    val data: List<TelnyxUsageRecord>
)

data class TelnyxUsageRecord(
    val timestamp: String,
    val data_mb: Long,
    val country: String,
    val network: String
)

/**
 * Adapter to convert Telnyx API to generic eSIM provider interface
 */
class TelnyxESIMProvider(
    private val api: TelnyxESIMApi,
    private val apiKey: String
) : ESIMProviderInterface {
    
    private val authHeader = "Bearer $apiKey"
    
    override suspend fun purchaseESIM(request: PurchaseESIMRequest): Result<PurchaseESIMResponse> {
        return try {
            val telnyxRequest = TelnyxPurchaseRequest(
                country_code = request.country,
                data_limit_mb = request.dataLimitMB
            )
            
            val response = api.purchaseESIM(authHeader, telnyxRequest)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!.data
                Result.success(
                    PurchaseESIMResponse(
                        esimId = data.id,
                        iccid = data.iccid,
                        activationCode = data.activation_code,
                        qrCode = data.qr_code_url,
                        status = data.status,
                        dataLimitMB = data.data_limit_mb,
                        validUntil = "", // Telnyx doesn't have expiry in this response
                        cost = 0.0, // Would need to calculate based on pricing
                        currency = "USD"
                    )
                )
            } else {
                Result.failure(Exception("Failed to purchase eSIM: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getESIMDetails(esimId: String): Result<ESIMDetailsResponse> {
        return try {
            val response = api.getESIM(authHeader, esimId)
            
            if (response.isSuccessful && response.body() != null) {
                val data = response.body()!!.data
                Result.success(
                    ESIMDetailsResponse(
                        esimId = data.id,
                        iccid = data.iccid,
                        status = data.status,
                        dataLimitMB = data.data_limit_mb,
                        dataUsedMB = data.data_used_mb,
                        dataRemainingMB = data.data_limit_mb - data.data_used_mb,
                        validUntil = "", // Would need additional API call
                        activatedAt = data.updated_at,
                        networks = emptyList() // Would need additional API call
                    )
                )
            } else {
                Result.failure(Exception("Failed to get eSIM details: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun activateESIM(esimId: String, request: ActivateESIMRequest): Result<ActivateESIMResponse> {
        return try {
            val response = api.enableESIM(authHeader, esimId)
            
            if (response.isSuccessful) {
                Result.success(
                    ActivateESIMResponse(
                        success = true,
                        message = "eSIM activated successfully",
                        activatedAt = response.body()?.data?.updated_at
                    )
                )
            } else {
                Result.failure(Exception("Failed to activate eSIM: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getDataUsage(esimId: String): Result<DataUsageResponse> {
        return try {
            val response = api.getUsage(authHeader, esimId)
            
            if (response.isSuccessful && response.body() != null) {
                val usageRecords = response.body()!!.data.map { record ->
                    UsageRecord(
                        timestamp = record.timestamp,
                        dataMB = record.data_mb,
                        country = record.country,
                        network = record.network
                    )
                }
                
                val totalUsed = usageRecords.sumOf { it.dataMB }
                
                Result.success(
                    DataUsageResponse(
                        esimId = esimId,
                        totalDataMB = 0, // Would need to get from eSIM details
                        usedDataMB = totalUsed,
                        remainingDataMB = 0, // Would calculate from total - used
                        usageHistory = usageRecords
                    )
                )
            } else {
                Result.failure(Exception("Failed to get usage data: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun addDataCredits(esimId: String, request: TopUpRequest): Result<TopUpResponse> {
        // Telnyx doesn't have a direct top-up API, would need to purchase additional data
        return Result.failure(Exception("Top-up not supported by Telnyx API"))
    }
}

/**
 * Generic interface for eSIM providers
 */
interface ESIMProviderInterface {
    suspend fun purchaseESIM(request: PurchaseESIMRequest): Result<PurchaseESIMResponse>
    suspend fun getESIMDetails(esimId: String): Result<ESIMDetailsResponse>
    suspend fun activateESIM(esimId: String, request: ActivateESIMRequest): Result<ActivateESIMResponse>
    suspend fun getDataUsage(esimId: String): Result<DataUsageResponse>
    suspend fun addDataCredits(esimId: String, request: TopUpRequest): Result<TopUpResponse>
}
