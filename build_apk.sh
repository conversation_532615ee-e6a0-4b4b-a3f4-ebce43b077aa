#!/bin/bash

# Free eSIM App - APK Build Script
# This script helps you build the APK from the project files

echo "🚀 Building Free eSIM App APK..."
echo "=================================="

# Check if we're in the right directory
if [ ! -f "settings.gradle.kts" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Make sure you have all the project files in the current directory"
    exit 1
fi

# Make gradlew executable
if [ -f "gradlew" ]; then
    chmod +x gradlew
    echo "✅ Made gradlew executable"
else
    echo "❌ Error: gradlew not found. Please ensure all project files are present."
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
./gradlew clean

# Build the APK
echo "🔨 Building APK..."
./gradlew assembleDebug

# Check if build was successful
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 SUCCESS! APK built successfully!"
    echo "=================================="
    echo "📱 APK Location: app/build/outputs/apk/debug/app-debug.apk"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Copy the APK to your Samsung Galaxy S25"
    echo "2. Enable 'Install unknown apps' in Settings"
    echo "3. Install the APK and enjoy your Free eSIM app!"
    echo ""
    echo "🔧 To install via ADB:"
    echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
    echo ""
else
    echo ""
    echo "❌ Build failed. Please check the error messages above."
    echo "💡 Common solutions:"
    echo "1. Make sure you have Android SDK installed"
    echo "2. Check that all project files are present"
    echo "3. Ensure you have internet connection for dependencies"
    echo "4. Try running: ./gradlew --refresh-dependencies assembleDebug"
fi
