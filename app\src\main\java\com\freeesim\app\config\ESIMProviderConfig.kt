package com.freeesim.app.config

/**
 * Configuration for different eSIM providers
 * This allows easy switching between providers without code changes
 */
object ESIMProviderConfig {
    
    enum class Provider {
        TELNYX,
        TWILIO,
        SORACOM,
        IBASIS, // Free trial available!
        DEMO // For testing without real API calls
    }
    
    // Current active provider - change this to switch providers
    val ACTIVE_PROVIDER = Provider.DEMO
    
    // Provider configurations
    object Telnyx {
        const val BASE_URL = "https://api.telnyx.com/"
        const val API_VERSION = "v2"
        
        // Pricing (example - would be dynamic in production)
        const val COST_PER_MB_USD = 0.01 // $0.01 per MB
        const val MINIMUM_PURCHASE_MB = 100L // 100MB minimum
        
        // Supported countries (example subset)
        val SUPPORTED_COUNTRIES = listOf(
            "US", "CA", "GB", "DE", "FR", "IT", "ES", "AU", "JP", "KR"
        )
    }
    
    object Twilio {
        const val BASE_URL = "https://wireless.twilio.com/"
        const val API_VERSION = "v1"
        
        // Pricing (example)
        const val COST_PER_MB_USD = 0.015 // $0.015 per MB
        const val MINIMUM_PURCHASE_MB = 50L // 50MB minimum
        
        val SUPPORTED_COUNTRIES = listOf(
            "US", "CA", "GB", "DE", "FR", "AU"
        )
    }
    
    object Soracom {
        const val BASE_URL = "https://api.soracom.io/"
        const val API_VERSION = "v1"
        
        // Pricing (example)
        const val COST_PER_MB_USD = 0.02 // $0.02 per MB
        const val MINIMUM_PURCHASE_MB = 10L // 10MB minimum
        
        val SUPPORTED_COUNTRIES = listOf(
            "US", "JP", "GB", "DE", "AU", "SG"
        )
    }
    
    // Demo configuration for testing
    object IBasis {
        const val BASE_URL = "https://api.ibasis.com/"
        const val API_VERSION = "v1"

        // Free trial pricing
        const val COST_PER_MB_USD = 0.0 // Free trial
        const val MINIMUM_PURCHASE_MB = 10L // 10MB minimum

        val SUPPORTED_COUNTRIES = listOf(
            "US", "CA", "GB", "DE", "FR", "IT", "ES", "AU", "JP", "KR", "IN", "BR", "CN", "RU"
        )
    }

    object Demo {
        const val COST_PER_MB_USD = 0.0 // Free for demo
        const val MINIMUM_PURCHASE_MB = 1L

        val SUPPORTED_COUNTRIES = listOf(
            "US", "CA", "GB", "DE", "FR", "IT", "ES", "AU", "JP", "KR", "IN", "BR"
        )
    }
    
    /**
     * Get pricing for current provider
     */
    fun getCostPerMB(): Double {
        return when (ACTIVE_PROVIDER) {
            Provider.TELNYX -> Telnyx.COST_PER_MB_USD
            Provider.TWILIO -> Twilio.COST_PER_MB_USD
            Provider.SORACOM -> Soracom.COST_PER_MB_USD
            Provider.IBASIS -> IBasis.COST_PER_MB_USD
            Provider.DEMO -> Demo.COST_PER_MB_USD
        }
    }
    
    /**
     * Get minimum purchase amount for current provider
     */
    fun getMinimumPurchaseMB(): Long {
        return when (ACTIVE_PROVIDER) {
            Provider.TELNYX -> Telnyx.MINIMUM_PURCHASE_MB
            Provider.TWILIO -> Twilio.MINIMUM_PURCHASE_MB
            Provider.SORACOM -> Soracom.MINIMUM_PURCHASE_MB
            Provider.DEMO -> Demo.MINIMUM_PURCHASE_MB
        }
    }
    
    /**
     * Get supported countries for current provider
     */
    fun getSupportedCountries(): List<String> {
        return when (ACTIVE_PROVIDER) {
            Provider.TELNYX -> Telnyx.SUPPORTED_COUNTRIES
            Provider.TWILIO -> Twilio.SUPPORTED_COUNTRIES
            Provider.SORACOM -> Soracom.SUPPORTED_COUNTRIES
            Provider.DEMO -> Demo.SUPPORTED_COUNTRIES
        }
    }
    
    /**
     * Calculate cost for given data amount
     */
    fun calculateCost(dataMB: Long): Double {
        return dataMB * getCostPerMB()
    }
    
    /**
     * Check if country is supported
     */
    fun isCountrySupported(countryCode: String): Boolean {
        return getSupportedCountries().contains(countryCode.uppercase())
    }
}
