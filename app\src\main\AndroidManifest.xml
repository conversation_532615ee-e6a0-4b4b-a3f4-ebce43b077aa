<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- eSIM and Telephony Permissions -->
    <uses-permission android:name="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- Internet and Network -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    
    <!-- AdMob -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    
    <!-- Camera for QR Code scanning -->
    <uses-permission android:name="android.permission.CAMERA" />
    
    <!-- Required for eSIM functionality -->
    <uses-feature
        android:name="android.hardware.telephony.euicc"
        android:required="true" />

    <application
        android:name=".FreeESIMApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.FreeESIM"
        tools:targetApi="31">
        
        <!-- AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713" />
        
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.FreeESIM">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- eSIM LPA Service -->
        <service
            android:name=".esim.ESIMService"
            android:permission="android.permission.BIND_EUICC_SERVICE"
            android:exported="true">
            <intent-filter android:priority="100">
                <action android:name="android.service.euicc.EuiccService" />
            </intent-filter>
        </service>
        
    </application>

</manifest>
