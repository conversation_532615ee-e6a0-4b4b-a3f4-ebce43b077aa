package com.freeesim.app.esim

import android.content.Context
import android.telephony.euicc.EuiccManager
import android.telephony.euicc.DownloadableSubscription
import android.app.PendingIntent
import android.content.Intent
import android.content.IntentFilter
import android.content.BroadcastReceiver
import android.telephony.euicc.EuiccInfo
import androidx.annotation.RequiresApi
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class ESIMManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val euiccManager: EuiccManager? by lazy {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
            context.getSystemService(Context.EUICC_SERVICE) as? EuiccManager
        } else null
    }
    
    @RequiresApi(android.os.Build.VERSION_CODES.P)
    fun isESIMSupported(): Boolean {
        return euiccManager?.isEnabled == true
    }
    
    @RequiresApi(android.os.Build.VERSION_CODES.P)
    fun getEuiccInfo(): EuiccInfo? {
        return euiccManager?.euiccInfo
    }
    
    @RequiresApi(android.os.Build.VERSION_CODES.P)
    suspend fun downloadSubscription(activationCode: String): ESIMDownloadResult {
        return suspendCancellableCoroutine { continuation ->
            val euicc = euiccManager
            if (euicc == null || !euicc.isEnabled) {
                continuation.resume(ESIMDownloadResult.Error("eSIM not supported or enabled"))
                return@suspendCancellableCoroutine
            }
            
            try {
                val subscription = DownloadableSubscription.forActivationCode(activationCode)
                
                val intent = Intent(ACTION_DOWNLOAD_SUBSCRIPTION)
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    0,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE
                )
                
                val receiver = object : BroadcastReceiver() {
                    override fun onReceive(context: Context?, intent: Intent?) {
                        val resultCode = getResultCode()
                        when (resultCode) {
                            EuiccManager.EMBEDDED_SUBSCRIPTION_RESULT_OK -> {
                                continuation.resume(ESIMDownloadResult.Success)
                            }
                            EuiccManager.EMBEDDED_SUBSCRIPTION_RESULT_ERROR -> {
                                val detailedCode = intent?.getIntExtra(
                                    EuiccManager.EXTRA_EMBEDDED_SUBSCRIPTION_DETAILED_CODE, 
                                    0
                                ) ?: 0
                                continuation.resume(
                                    ESIMDownloadResult.Error("Download failed with code: $detailedCode")
                                )
                            }
                            else -> {
                                continuation.resume(
                                    ESIMDownloadResult.Error("Unknown result code: $resultCode")
                                )
                            }
                        }
                        context?.unregisterReceiver(this)
                    }
                }
                
                context.registerReceiver(receiver, IntentFilter(ACTION_DOWNLOAD_SUBSCRIPTION))
                euicc.downloadSubscription(subscription, true, pendingIntent)
                
                continuation.invokeOnCancellation {
                    try {
                        context.unregisterReceiver(receiver)
                    } catch (e: Exception) {
                        // Receiver might already be unregistered
                    }
                }
                
            } catch (e: Exception) {
                continuation.resume(ESIMDownloadResult.Error("Exception: ${e.message}"))
            }
        }
    }
    
    companion object {
        private const val ACTION_DOWNLOAD_SUBSCRIPTION = "com.freeesim.app.DOWNLOAD_SUBSCRIPTION"
    }
}

sealed class ESIMDownloadResult {
    object Success : ESIMDownloadResult()
    data class Error(val message: String) : ESIMDownloadResult()
}
