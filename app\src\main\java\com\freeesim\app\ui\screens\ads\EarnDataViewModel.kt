package com.freeesim.app.ui.screens.ads

import androidx.activity.ComponentActivity
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.freeesim.app.ads.AdManager
import com.freeesim.app.ads.AdShowResult
import com.freeesim.app.data.repository.UserRepository
import com.freeesim.app.data.model.AdType
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class EarnDataViewModel @Inject constructor(
    private val adManager: AdManager,
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(EarnDataUiState())
    val uiState: StateFlow<EarnDataUiState> = _uiState.asStateFlow()
    
    init {
        loadAds()
        loadTodayStats()
    }
    
    private fun loadAds() {
        viewModelScope.launch {
            // Load rewarded ad
            _uiState.value = _uiState.value.copy(isLoadingRewardedAd = true)
            val rewardedResult = adManager.loadRewardedAd()
            _uiState.value = _uiState.value.copy(
                isLoadingRewardedAd = false,
                isRewardedAdAvailable = rewardedResult is com.freeesim.app.ads.AdLoadResult.Success
            )
            
            // Load interstitial ad
            _uiState.value = _uiState.value.copy(isLoadingInterstitialAd = true)
            val interstitialResult = adManager.loadInterstitialAd()
            _uiState.value = _uiState.value.copy(
                isLoadingInterstitialAd = false,
                isInterstitialAdAvailable = interstitialResult is com.freeesim.app.ads.AdLoadResult.Success
            )
        }
    }
    
    private fun loadTodayStats() {
        viewModelScope.launch {
            // In a real app, you'd calculate today's earnings from the database
            // For now, using placeholder values
            _uiState.value = _uiState.value.copy(
                todayEarnings = 250L,
                adsWatchedToday = 3,
                isDailyBonusAvailable = true // Check if user already claimed today
            )
        }
    }
    
    fun showRewardedAd(activity: ComponentActivity) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoadingRewardedAd = true)
            
            when (val result = adManager.showRewardedAd(activity)) {
                is AdShowResult.Rewarded -> {
                    // Record the reward
                    userRepository.recordAdReward(
                        adType = AdType.REWARDED_VIDEO,
                        rewardDataMB = result.dataMB,
                        adNetworkId = "admob"
                    )
                    
                    _uiState.value = _uiState.value.copy(
                        message = "Earned ${result.dataMB} MB!",
                        todayEarnings = _uiState.value.todayEarnings + result.dataMB,
                        adsWatchedToday = _uiState.value.adsWatchedToday + 1,
                        isRewardedAdAvailable = false
                    )
                    
                    // Load next ad
                    loadAds()
                }
                is AdShowResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        message = "Failed to show ad: ${result.message}"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        message = "Ad completed but no reward received"
                    )
                }
            }
            
            _uiState.value = _uiState.value.copy(isLoadingRewardedAd = false)
        }
    }
    
    fun showInterstitialAd(activity: ComponentActivity) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoadingInterstitialAd = true)
            
            when (val result = adManager.showInterstitialAd(activity)) {
                is AdShowResult.Completed -> {
                    // Record the reward
                    userRepository.recordAdReward(
                        adType = AdType.INTERSTITIAL,
                        rewardDataMB = result.dataMB,
                        adNetworkId = "admob"
                    )
                    
                    _uiState.value = _uiState.value.copy(
                        message = "Earned ${result.dataMB} MB!",
                        todayEarnings = _uiState.value.todayEarnings + result.dataMB,
                        adsWatchedToday = _uiState.value.adsWatchedToday + 1,
                        isInterstitialAdAvailable = false
                    )
                    
                    // Load next ad
                    loadAds()
                }
                is AdShowResult.Error -> {
                    _uiState.value = _uiState.value.copy(
                        message = "Failed to show ad: ${result.message}"
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        message = "Ad completed"
                    )
                }
            }
            
            _uiState.value = _uiState.value.copy(isLoadingInterstitialAd = false)
        }
    }
    
    fun claimDailyBonus() {
        viewModelScope.launch {
            userRepository.addDataCredits(50L)
            _uiState.value = _uiState.value.copy(
                message = "Daily bonus claimed! +50 MB",
                todayEarnings = _uiState.value.todayEarnings + 50L,
                isDailyBonusAvailable = false
            )
        }
    }
    
    fun clearMessage() {
        _uiState.value = _uiState.value.copy(message = null)
    }
}

data class EarnDataUiState(
    val todayEarnings: Long = 0L,
    val adsWatchedToday: Int = 0,
    val isRewardedAdAvailable: Boolean = false,
    val isInterstitialAdAvailable: Boolean = false,
    val isLoadingRewardedAd: Boolean = false,
    val isLoadingInterstitialAd: Boolean = false,
    val isDailyBonusAvailable: Boolean = false,
    val message: String? = null
)
