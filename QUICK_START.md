# Quick Start Guide

## 🚀 Run the App in 5 Minutes

### Prerequisites
- Android Studio (latest version)
- Android SDK 28+ 
- An Android device or emulator

### Step 1: Clone and Open
```bash
# If you haven't already, copy all the files to a new directory
# Open Android Studio and select "Open an existing project"
# Navigate to the project directory
```

### Step 2: Sync Project
1. Android Studio will automatically detect it's a Gradle project
2. Click "Sync Now" when prompted
3. Wait for Gradle sync to complete

### Step 3: Run the App
1. Click the green "Run" button (▶️) in Android Studio
2. Select your device/emulator
3. The app will build and install automatically

## 📱 What You'll See

### Home Screen
- Welcome interface with data credits display
- Quick action buttons for eSIM setup and earning data
- Clean Material Design 3 interface

### eSIM Setup (Demo Mode)
- Compatibility check (simulated)
- eSIM purchase and activation flow
- Success confirmation

### Earn Data Screen
- Watch rewarded video ads (test ads)
- View interstitial ads
- Daily bonus system
- Real-time credit updates

## 🧪 Testing Features

### Demo Mode Features
All eSIM operations are simulated, so you can test:
- ✅ eSIM purchase flow (2-second delay simulation)
- ✅ eSIM activation (3-second delay simulation)
- ✅ Data usage tracking
- ✅ Credit management
- ✅ Ad watching and rewards

### Ad Testing
- Uses Google AdMob test ad units
- Safe to click and interact with
- Real ad loading and display
- Reward system integration

## 🔧 Troubleshooting

### Common Issues

**1. Gradle Sync Failed**
```bash
# In Android Studio terminal:
./gradlew clean
./gradlew build
```

**2. Missing Google Services**
- The `google-services.json` is a demo file
- For production, replace with your Firebase project file

**3. eSIM Permissions**
- Some eSIM features require real device testing
- Emulator may show permission warnings (normal)

**4. Ad Loading Issues**
- Test ads should load immediately
- Check internet connection
- AdMob test IDs are pre-configured

### Build Variants
```bash
# Debug build (default)
./gradlew assembleDebug

# Release build
./gradlew assembleRelease
```

## 📊 Demo Data

The app comes with realistic demo data:
- **Initial Credits**: 100 MB welcome bonus
- **Ad Rewards**: 100 MB per video, 25 MB per interstitial
- **Daily Bonus**: 50 MB per day
- **Demo eSIM**: Simulated 1GB data plan

## 🎯 Next Steps After Testing

1. **Choose Real Provider**: Switch from DEMO to TELNYX/TWILIO
2. **Get API Keys**: Sign up with chosen provider
3. **Production AdMob**: Replace test ad units
4. **Real Device Testing**: Test eSIM on compatible device
5. **Play Store**: Prepare for publication

## 📱 Device Requirements for Full Testing

### eSIM Compatible Devices
- Google Pixel 3+ 
- iPhone XS+ (for reference)
- Samsung Galaxy S20+
- OnePlus 7T+
- Many other modern Android devices

### Check eSIM Support
```kotlin
// The app automatically checks this
if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
    val euiccManager = getSystemService(Context.EUICC_SERVICE) as EuiccManager
    val isSupported = euiccManager.isEnabled
}
```

## 🐛 Known Demo Limitations

- eSIM installation is simulated (won't actually install)
- Data usage tracking is simulated
- Real network connectivity not established
- Some permissions may show warnings

These are normal for demo mode and will work with real providers!

## 📞 Need Help?

1. **Check Android Studio logs** for detailed error messages
2. **Verify all files** are in correct locations
3. **Ensure internet connection** for ad loading
4. **Try clean rebuild** if issues persist

The app should run smoothly in demo mode on any Android device/emulator!
