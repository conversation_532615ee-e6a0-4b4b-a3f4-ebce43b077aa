package com.freeesim.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.freeesim.app.data.model.User
import com.freeesim.app.data.model.DataUsage
import com.freeesim.app.data.model.AdReward
import com.freeesim.app.data.model.ESIMProfile

@Database(
    entities = [
        User::class,
        DataUsage::class,
        AdReward::class,
        ESIMProfile::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class FreeESIMDatabase : RoomDatabase() {
    
    abstract fun userDao(): UserDao
    abstract fun dataUsageDao(): DataUsageDao
    abstract fun adRewardDao(): AdRewardDao
    abstract fun esimProfileDao(): ESIMProfileDao
    
    companion object {
        @Volatile
        private var INSTANCE: FreeESIMDatabase? = null
        
        fun getDatabase(context: Context): FreeESIMDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    FreeESIMDatabase::class.java,
                    "free_esim_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
