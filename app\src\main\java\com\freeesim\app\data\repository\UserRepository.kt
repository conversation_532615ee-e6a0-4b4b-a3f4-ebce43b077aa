package com.freeesim.app.data.repository

import com.freeesim.app.data.database.UserDao
import com.freeesim.app.data.database.AdRewardDao
import com.freeesim.app.data.model.User
import com.freeesim.app.data.model.AdReward
import com.freeesim.app.data.model.AdType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val userDao: UserDao,
    private val adRewardDao: AdRewardDao
) {
    
    private val currentUserId = "default_user" // In production, this would come from authentication
    
    suspend fun getCurrentUser(): Flow<User?> {
        // Create default user if doesn't exist
        val existingUser = userDao.getUser(currentUserId).first()
        if (existingUser == null) {
            val defaultUser = User(
                id = currentUserId,
                email = "<EMAIL>", // Would come from auth
                dataCredits = 100L // Welcome bonus
            )
            userDao.insertUser(defaultUser)
        }
        return userDao.getUser(currentUserId)
    }
    
    suspend fun addDataCredits(credits: Long) {
        userDao.addDataCredits(currentUserId, credits)
    }
    
    suspend fun deductDataUsage(usage: Long) {
        userDao.deductDataUsage(currentUserId, usage)
    }
    
    suspend fun recordAdReward(adType: AdType, rewardDataMB: Long, adNetworkId: String): String {
        val rewardId = UUID.randomUUID().toString()
        val adReward = AdReward(
            id = rewardId,
            userId = currentUserId,
            adType = adType,
            rewardDataMB = rewardDataMB,
            adNetworkId = adNetworkId
        )
        adRewardDao.insertAdReward(adReward)
        
        // Add credits to user account
        addDataCredits(rewardDataMB)
        
        return rewardId
    }
    
    suspend fun claimAdReward(rewardId: String) {
        adRewardDao.claimReward(rewardId)
    }
    
    fun getUserAdRewards(): Flow<List<AdReward>> {
        return adRewardDao.getUserAdRewards(currentUserId)
    }
    
    suspend fun updateESIMStatus(isActivated: Boolean, iccid: String? = null) {
        val user = getCurrentUser().first()
        user?.let {
            val updatedUser = it.copy(
                isESIMActivated = isActivated,
                esimIccid = iccid
            )
            userDao.updateUser(updatedUser)
        }
    }
}
