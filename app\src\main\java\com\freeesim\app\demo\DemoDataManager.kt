package com.freeesim.app.demo

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages demo data and realistic simulations for the app
 * This makes the demo experience feel completely real
 */
@Singleton
class DemoDataManager @Inject constructor() {
    
    private val _demoStats = MutableStateFlow(DemoStats())
    val demoStats: StateFlow<DemoStats> = _demoStats.asStateFlow()
    
    data class DemoStats(
        val totalUsers: Int = 15847,
        val activeUsers: Int = 8923,
        val totalDataEarned: Long = 2847392L, // MB
        val totalAdsWatched: Int = 94821,
        val averageEarningsPerUser: Long = 180L, // MB per user
        val topCountries: List<String> = listOf("US", "CA", "GB", "DE", "AU"),
        val networkQuality: Float = 0.97f, // 97% uptime
        val userSatisfaction: Float = 4.8f // 4.8/5 stars
    )
    
    /**
     * Simulates realistic app usage statistics
     */
    fun updateDemoStats() {
        val current = _demoStats.value
        _demoStats.value = current.copy(
            totalUsers = current.totalUsers + kotlin.random.Random.nextInt(1, 5),
            activeUsers = current.activeUsers + kotlin.random.Random.nextInt(-2, 3),
            totalDataEarned = current.totalDataEarned + kotlin.random.Random.nextLong(50, 200),
            totalAdsWatched = current.totalAdsWatched + kotlin.random.Random.nextInt(5, 15)
        )
    }
    
    /**
     * Generates realistic user testimonials
     */
    fun getRandomTestimonial(): String {
        val testimonials = listOf(
            "🔥 Amazing app! I've earned 2GB this week just watching ads while commuting!",
            "💯 Finally, truly free mobile data. Works perfectly with YouTube and games!",
            "🚀 Setup took literally 1 minute. Now I have unlimited data for free!",
            "⭐ Best app ever! Saved me $50/month on my phone bill.",
            "🎯 Love how I can earn data by watching ads. So much better than paying!",
            "🌍 Works everywhere I travel. Global coverage is incredible!",
            "📱 All my apps work perfectly. Gaming, streaming, everything!",
            "💰 Earned 5GB this month! This app is a game changer.",
            "🎬 Ads are actually interesting and I get rewarded. Win-win!",
            "✨ Simple, fast, and actually free. Highly recommend!"
        )
        return testimonials.random()
    }
    
    /**
     * Generates realistic app usage tips
     */
    fun getRandomTip(): String {
        val tips = listOf(
            "💡 Tip: Watch ads during your commute to maximize earnings!",
            "🎯 Pro tip: Check the app daily for bonus rewards and special offers.",
            "⚡ Quick tip: Video ads give 4x more data than banner ads.",
            "🌟 Tip: Share with friends to unlock referral bonuses!",
            "📊 Smart tip: Monitor your usage to optimize your earning strategy.",
            "🎮 Gaming tip: Earn extra data before playing online games.",
            "📺 Streaming tip: Watch a few ads to earn data for Netflix/YouTube.",
            "🌍 Travel tip: Download the app works in 150+ countries worldwide.",
            "⏰ Time tip: Ads refresh every hour for maximum earning potential.",
            "🔋 Battery tip: Ads are optimized to use minimal battery power."
        )
        return tips.random()
    }
    
    /**
     * Simulates realistic network performance
     */
    fun getNetworkStatus(): NetworkStatus {
        val random = kotlin.random.Random
        return when (random.nextFloat()) {
            in 0f..0.85f -> NetworkStatus.EXCELLENT
            in 0.85f..0.95f -> NetworkStatus.GOOD
            in 0.95f..0.99f -> NetworkStatus.FAIR
            else -> NetworkStatus.POOR
        }
    }
    
    enum class NetworkStatus(val displayName: String, val emoji: String) {
        EXCELLENT("Excellent", "🟢"),
        GOOD("Good", "🟡"),
        FAIR("Fair", "🟠"),
        POOR("Poor", "🔴")
    }
    
    /**
     * Generates realistic data usage patterns
     */
    fun simulateAppUsage(): List<AppUsage> {
        return listOf(
            AppUsage("YouTube", 45L, "📺"),
            AppUsage("Instagram", 23L, "📷"),
            AppUsage("TikTok", 38L, "🎵"),
            AppUsage("WhatsApp", 12L, "💬"),
            AppUsage("Spotify", 15L, "🎶"),
            AppUsage("Chrome", 28L, "🌐"),
            AppUsage("Gmail", 8L, "📧"),
            AppUsage("Maps", 19L, "🗺️"),
            AppUsage("COD Mobile", 67L, "🎮"),
            AppUsage("Netflix", 89L, "🎬")
        ).shuffled().take(6)
    }
    
    data class AppUsage(
        val appName: String,
        val dataMB: Long,
        val emoji: String
    )
    
    /**
     * Generates realistic earning opportunities
     */
    fun getEarningOpportunities(): List<EarningOpportunity> {
        return listOf(
            EarningOpportunity(
                title = "🎬 Premium Video Ad",
                description = "Watch a 30-second premium ad",
                reward = 150L,
                timeRequired = "30 seconds",
                available = true
            ),
            EarningOpportunity(
                title = "📱 App Install Bonus",
                description = "Install and try a featured app",
                reward = 500L,
                timeRequired = "2 minutes",
                available = kotlin.random.Random.nextBoolean()
            ),
            EarningOpportunity(
                title = "📋 Quick Survey",
                description = "Answer 3 quick questions",
                reward = 75L,
                timeRequired = "1 minute",
                available = kotlin.random.Random.nextBoolean()
            ),
            EarningOpportunity(
                title = "🎯 Daily Challenge",
                description = "Complete today's earning challenge",
                reward = 200L,
                timeRequired = "5 minutes",
                available = true
            )
        )
    }
    
    data class EarningOpportunity(
        val title: String,
        val description: String,
        val reward: Long,
        val timeRequired: String,
        val available: Boolean
    )
    
    /**
     * Simulates realistic global coverage
     */
    fun getGlobalCoverage(): List<CountryCoverage> {
        return listOf(
            CountryCoverage("🇺🇸 United States", "Excellent", 99),
            CountryCoverage("🇨🇦 Canada", "Excellent", 98),
            CountryCoverage("🇬🇧 United Kingdom", "Excellent", 97),
            CountryCoverage("🇩🇪 Germany", "Excellent", 96),
            CountryCoverage("🇫🇷 France", "Good", 94),
            CountryCoverage("🇦🇺 Australia", "Good", 93),
            CountryCoverage("🇯🇵 Japan", "Excellent", 98),
            CountryCoverage("🇰🇷 South Korea", "Excellent", 99),
            CountryCoverage("🇮🇳 India", "Good", 89),
            CountryCoverage("🇧🇷 Brazil", "Fair", 85)
        )
    }
    
    data class CountryCoverage(
        val country: String,
        val quality: String,
        val coverage: Int
    )
    
    /**
     * Generates motivational messages based on usage
     */
    fun getMotivationalMessage(dataCredits: Long): String {
        return when {
            dataCredits >= 1000 -> "🔥 You're crushing it! Over 1GB earned!"
            dataCredits >= 500 -> "🚀 Awesome progress! You're halfway to 1GB!"
            dataCredits >= 100 -> "⭐ Great start! Keep watching ads to earn more!"
            else -> "💪 Let's get started! Watch your first ad to earn 100MB!"
        }
    }
    
    /**
     * Simulates realistic success stories
     */
    fun getSuccessStory(): String {
        val stories = listOf(
            "Sarah from NYC earned 15GB last month and canceled her $80 phone plan! 💰",
            "Mike travels the world and uses Free eSIM in 25+ countries. Never pays roaming! 🌍",
            "College student Emma earned 50GB this semester. Perfect for streaming lectures! 🎓",
            "Gamer Alex earned 100GB for COD Mobile tournaments. Zero lag, maximum wins! 🎮",
            "Family of 4 saves $200/month using Free eSIM for all their devices! 👨‍👩‍👧‍👦"
        )
        return stories.random()
    }
}
