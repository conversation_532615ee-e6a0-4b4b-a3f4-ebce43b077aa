package com.freeesim.app.ads

import android.app.Activity
import android.content.Context
import com.google.android.gms.ads.*
import com.google.android.gms.ads.rewarded.RewardedAd
import com.google.android.gms.ads.rewarded.RewardedAdLoadCallback
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.suspendCancellableCoroutine
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class AdManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private var rewardedAd: RewardedAd? = null
    private var interstitialAd: InterstitialAd? = null
    
    // Test ad unit IDs - replace with real ones in production
    companion object {
        private const val REWARDED_AD_UNIT_ID = "ca-app-pub-3940256099942544/5224354917"
        private const val INTERSTITIAL_AD_UNIT_ID = "ca-app-pub-3940256099942544/1033173712"
        private const val BANNER_AD_UNIT_ID = "ca-app-pub-3940256099942544/6300978111"
        
        // Data rewards in MB
        const val REWARDED_VIDEO_DATA_MB = 100L
        const val INTERSTITIAL_DATA_MB = 25L
        const val BANNER_VIEW_DATA_MB = 5L
    }
    
    suspend fun loadRewardedAd(): AdLoadResult {
        return suspendCancellableCoroutine { continuation ->
            val adRequest = AdRequest.Builder().build()
            
            RewardedAd.load(
                context,
                REWARDED_AD_UNIT_ID,
                adRequest,
                object : RewardedAdLoadCallback() {
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        rewardedAd = null
                        continuation.resume(AdLoadResult.Error(adError.message))
                    }
                    
                    override fun onAdLoaded(ad: RewardedAd) {
                        rewardedAd = ad
                        continuation.resume(AdLoadResult.Success)
                    }
                }
            )
        }
    }
    
    suspend fun showRewardedAd(activity: Activity): AdShowResult {
        return suspendCancellableCoroutine { continuation ->
            val ad = rewardedAd
            if (ad == null) {
                continuation.resume(AdShowResult.Error("Ad not loaded"))
                return@suspendCancellableCoroutine
            }
            
            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdClicked() {
                    // Ad was clicked
                }
                
                override fun onAdDismissedFullScreenContent() {
                    // Ad was dismissed
                    rewardedAd = null
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    // Ad failed to show
                    rewardedAd = null
                    continuation.resume(AdShowResult.Error(adError.message))
                }
                
                override fun onAdImpression() {
                    // Ad impression recorded
                }
                
                override fun onAdShowedFullScreenContent() {
                    // Ad showed full screen content
                }
            }
            
            ad.show(activity) { rewardItem ->
                // User earned reward
                continuation.resume(
                    AdShowResult.Rewarded(
                        rewardAmount = rewardItem.amount,
                        rewardType = rewardItem.type,
                        dataMB = REWARDED_VIDEO_DATA_MB
                    )
                )
            }
        }
    }
    
    suspend fun loadInterstitialAd(): AdLoadResult {
        return suspendCancellableCoroutine { continuation ->
            val adRequest = AdRequest.Builder().build()
            
            InterstitialAd.load(
                context,
                INTERSTITIAL_AD_UNIT_ID,
                adRequest,
                object : InterstitialAdLoadCallback() {
                    override fun onAdFailedToLoad(adError: LoadAdError) {
                        interstitialAd = null
                        continuation.resume(AdLoadResult.Error(adError.message))
                    }
                    
                    override fun onAdLoaded(ad: InterstitialAd) {
                        interstitialAd = ad
                        continuation.resume(AdLoadResult.Success)
                    }
                }
            )
        }
    }
    
    suspend fun showInterstitialAd(activity: Activity): AdShowResult {
        return suspendCancellableCoroutine { continuation ->
            val ad = interstitialAd
            if (ad == null) {
                continuation.resume(AdShowResult.Error("Ad not loaded"))
                return@suspendCancellableCoroutine
            }
            
            ad.fullScreenContentCallback = object : FullScreenContentCallback() {
                override fun onAdDismissedFullScreenContent() {
                    interstitialAd = null
                    continuation.resume(
                        AdShowResult.Completed(dataMB = INTERSTITIAL_DATA_MB)
                    )
                }
                
                override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                    interstitialAd = null
                    continuation.resume(AdShowResult.Error(adError.message))
                }
            }
            
            ad.show(activity)
        }
    }
    
    fun createBannerAd(): AdView {
        return AdView(context).apply {
            setAdSize(AdSize.BANNER)
            adUnitId = BANNER_AD_UNIT_ID
        }
    }
    
    fun isRewardedAdReady(): Boolean = rewardedAd != null
    fun isInterstitialAdReady(): Boolean = interstitialAd != null
}

sealed class AdLoadResult {
    object Success : AdLoadResult()
    data class Error(val message: String) : AdLoadResult()
}

sealed class AdShowResult {
    data class Rewarded(
        val rewardAmount: Int,
        val rewardType: String,
        val dataMB: Long
    ) : AdShowResult()
    
    data class Completed(val dataMB: Long) : AdShowResult()
    data class Error(val message: String) : AdShowResult()
}
