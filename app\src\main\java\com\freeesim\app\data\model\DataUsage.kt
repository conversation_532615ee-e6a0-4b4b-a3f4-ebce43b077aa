package com.freeesim.app.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "data_usage")
data class DataUsage(
    @PrimaryKey
    val id: String,
    val userId: String,
    val appPackageName: String,
    val dataUsedMB: Long,
    val timestamp: Long = System.currentTimeMillis(),
    val sessionDurationMs: Long
)

@Entity(tableName = "ad_rewards")
data class AdReward(
    @PrimaryKey
    val id: String,
    val userId: String,
    val adType: AdType,
    val rewardDataMB: Long,
    val adNetworkId: String,
    val timestamp: Long = System.currentTimeMillis(),
    val isRewardClaimed: Boolean = false
)

enum class AdType {
    REWARDED_VIDEO,
    INTERSTITIAL,
    BANNER
}

@Entity(tableName = "esim_profiles")
data class ESIMProfile(
    @PrimaryKey
    val iccid: String,
    val userId: String,
    val activationCode: String,
    val carrierName: String,
    val isActive: Boolean = false,
    val dataAllowanceMB: Long,
    val dataUsedMB: Long = 0,
    val expiryDate: Long? = null,
    val createdAt: Long = System.currentTimeMillis()
)
