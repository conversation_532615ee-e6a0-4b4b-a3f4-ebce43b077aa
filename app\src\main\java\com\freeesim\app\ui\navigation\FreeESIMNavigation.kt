package com.freeesim.app.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.freeesim.app.ui.screens.home.HomeScreen
import com.freeesim.app.ui.screens.esim.ESIMSetupScreen
import com.freeesim.app.ui.screens.ads.EarnDataScreen
import com.freeesim.app.ui.screens.usage.DataUsageScreen
import com.freeesim.app.ui.screens.profile.ProfileScreen

@Composable
fun FreeESIMNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController()
) {
    NavHost(
        navController = navController,
        startDestination = "home",
        modifier = modifier
    ) {
        composable("home") {
            HomeScreen(
                onNavigateToESIMSetup = { navController.navigate("esim_setup") },
                onNavigateToEarnData = { navController.navigate("earn_data") },
                onNavigateToDataUsage = { navController.navigate("data_usage") },
                onNavigateToProfile = { navController.navigate("profile") }
            )
        }
        
        composable("esim_setup") {
            ESIMSetupScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("earn_data") {
            EarnDataScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("data_usage") {
            DataUsageScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("profile") {
            ProfileScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
    }
}
