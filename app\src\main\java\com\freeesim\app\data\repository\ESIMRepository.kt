package com.freeesim.app.data.repository

import com.freeesim.app.data.api.*
import com.freeesim.app.data.database.ESIMProfileDao
import com.freeesim.app.data.model.ESIMProfile
import kotlinx.coroutines.flow.Flow
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ESIMRepository @Inject constructor(
    private val esimProfileDao: ESIMProfileDao,
    private val esimProvider: ESIMProviderInterface
) {
    
    private val currentUserId = "default_user" // In production, this would come from authentication
    
    suspend fun purchaseESIM(
        country: String? = null,
        dataLimitMB: Long = 1000L // Default 1GB
    ): Result<ESIMProfile> {
        return try {
            val request = PurchaseESIMRequest(
                country = country,
                dataLimitMB = dataLimitMB
            )
            
            val result = esimProvider.purchaseESIM(request)
            
            result.fold(
                onSuccess = { response ->
                    // Save to local database
                    val profile = ESIMProfile(
                        iccid = response.iccid,
                        userId = currentUserId,
                        activationCode = response.activationCode,
                        carrierName = "Telnyx", // Would be dynamic based on provider
                        isActive = false,
                        dataAllowanceMB = response.dataLimitMB,
                        dataUsedMB = 0L
                    )
                    
                    esimProfileDao.insertESIMProfile(profile)
                    Result.success(profile)
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun activateESIM(iccid: String): Result<Boolean> {
        return try {
            val profile = esimProfileDao.getESIMProfile(iccid)
                ?: return Result.failure(Exception("eSIM profile not found"))
            
            val request = ActivateESIMRequest()
            val result = esimProvider.activateESIM(profile.iccid, request)
            
            result.fold(
                onSuccess = { response ->
                    if (response.success) {
                        // Update local database
                        val updatedProfile = profile.copy(isActive = true)
                        esimProfileDao.updateESIMProfile(updatedProfile)
                        Result.success(true)
                    } else {
                        Result.failure(Exception(response.message))
                    }
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getESIMDetails(iccid: String): Result<ESIMProfile> {
        return try {
            val localProfile = esimProfileDao.getESIMProfile(iccid)
                ?: return Result.failure(Exception("eSIM profile not found"))
            
            // Get updated details from provider
            val result = esimProvider.getESIMDetails(localProfile.iccid)
            
            result.fold(
                onSuccess = { response ->
                    // Update local database with latest info
                    val updatedProfile = localProfile.copy(
                        isActive = response.status == "active",
                        dataAllowanceMB = response.dataLimitMB,
                        dataUsedMB = response.dataUsedMB
                    )
                    
                    esimProfileDao.updateESIMProfile(updatedProfile)
                    Result.success(updatedProfile)
                },
                onFailure = { error ->
                    // Return local data if API fails
                    Result.success(localProfile)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun addDataToESIM(iccid: String, dataMB: Long): Result<Boolean> {
        return try {
            val profile = esimProfileDao.getESIMProfile(iccid)
                ?: return Result.failure(Exception("eSIM profile not found"))
            
            val request = TopUpRequest(dataMB = dataMB)
            val result = esimProvider.addDataCredits(profile.iccid, request)
            
            result.fold(
                onSuccess = { response ->
                    if (response.success) {
                        // Update local database
                        val updatedProfile = profile.copy(
                            dataAllowanceMB = response.newDataLimitMB
                        )
                        esimProfileDao.updateESIMProfile(updatedProfile)
                        Result.success(true)
                    } else {
                        Result.failure(Exception("Failed to add data"))
                    }
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun syncDataUsage(iccid: String): Result<Long> {
        return try {
            val result = esimProvider.getDataUsage(iccid)
            
            result.fold(
                onSuccess = { response ->
                    // Update local database
                    val profile = esimProfileDao.getESIMProfile(iccid)
                    if (profile != null) {
                        val updatedProfile = profile.copy(
                            dataUsedMB = response.usedDataMB
                        )
                        esimProfileDao.updateESIMProfile(updatedProfile)
                    }
                    Result.success(response.usedDataMB)
                },
                onFailure = { error ->
                    Result.failure(error)
                }
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun getUserESIMProfiles(): Flow<List<ESIMProfile>> {
        return esimProfileDao.getUserESIMProfiles(currentUserId)
    }
    
    suspend fun getActiveESIMProfile(): ESIMProfile? {
        val profiles = esimProfileDao.getUserESIMProfiles(currentUserId)
        // In a real app, you'd collect the flow and find the active one
        // For now, return null as placeholder
        return null
    }
}
