# 📱 Build Your Free eSIM App APK

Complete instructions to build the APK from your project files.

## 🚀 **Quick Build (Recommended)**

### **Windows Users:**
```cmd
# Double-click build_apk.bat
# OR run in Command Prompt:
build_apk.bat
```

### **Mac/Linux Users:**
```bash
# Make script executable and run:
chmod +x build_apk.sh
./build_apk.sh
```

## 🔧 **Manual Build Steps**

### **Prerequisites:**
- ✅ Android Studio installed
- ✅ Android SDK (API 28+)
- ✅ Java 8+ installed
- ✅ All project files in one directory

### **Step 1: Setup Project**
1. **Create new folder** for your project
2. **Copy ALL files** from our conversation into this folder
3. **Maintain the exact folder structure** shown below

### **Step 2: Build APK**

**Option A: Android Studio (Easiest)**
1. Open Android Studio
2. File → Open → Select your project folder
3. Wait for Gradle sync
4. Build → Build Bundle(s) / APK(s) → Build APK(s)
5. APK created at: `app/build/outputs/apk/debug/app-debug.apk`

**Option B: Command Line**
```bash
# Navigate to project directory
cd /path/to/your/project

# Build APK
./gradlew assembleDebug        # Mac/Linux
gradlew.bat assembleDebug      # Windows
```

## 📁 **Required Project Structure**

```
FreeESIMApp/
├── 📄 build.gradle.kts
├── 📄 settings.gradle.kts
├── 📄 gradle.properties
├── 📄 google-services.json
├── 📄 build_apk.sh
├── 📄 build_apk.bat
├── 📁 gradle/wrapper/
│   └── 📄 gradle-wrapper.properties
└── 📁 app/
    ├── 📄 build.gradle.kts
    ├── 📄 proguard-rules.pro
    └── 📁 src/
        ├── 📁 main/
        │   ├── 📄 AndroidManifest.xml
        │   ├── 📁 java/com/freeesim/app/
        │   │   ├── 📄 MainActivity.kt
        │   │   ├── 📄 FreeESIMApplication.kt
        │   │   ├── 📁 data/
        │   │   │   ├── 📁 api/
        │   │   │   ├── 📁 database/
        │   │   │   ├── 📁 model/
        │   │   │   └── 📁 repository/
        │   │   ├── 📁 ui/
        │   │   │   ├── 📁 screens/
        │   │   │   ├── 📁 navigation/
        │   │   │   └── 📁 theme/
        │   │   ├── 📁 esim/
        │   │   ├── 📁 ads/
        │   │   ├── 📁 config/
        │   │   ├── 📁 demo/
        │   │   └── 📁 di/
        │   └── 📁 res/
        │       ├── 📁 values/
        │       │   ├── 📄 strings.xml
        │       │   └── 📄 themes.xml
        │       └── 📁 xml/
        │           ├── 📄 data_extraction_rules.xml
        │           └── 📄 backup_rules.xml
        └── 📁 test/
            └── 📁 java/com/freeesim/app/
                └── 📄 DemoESIMProviderTest.kt
```

## ⚡ **Quick Checklist**

Before building, ensure you have:
- [ ] All 40+ project files copied correctly
- [ ] Proper folder structure maintained
- [ ] Android Studio or Android SDK installed
- [ ] Internet connection for dependencies
- [ ] Java 8+ installed

## 🎯 **Expected Build Output**

**✅ Successful Build:**
```
BUILD SUCCESSFUL in 2m 15s
47 actionable tasks: 47 executed

📱 APK Location: app/build/outputs/apk/debug/app-debug.apk
📦 APK Size: ~15-25 MB
🎯 Target: Android 9+ (API 28+)
```

## 📱 **Install on Samsung S25**

**Method 1: ADB (Recommended)**
```bash
# Connect S25 via USB, enable USB debugging
adb install app/build/outputs/apk/debug/app-debug.apk
```

**Method 2: Direct Install**
1. Copy APK to your Samsung S25
2. Settings → Apps → Special access → Install unknown apps
3. Enable for your file manager
4. Open APK file and install

## 🔧 **Troubleshooting**

### **Common Build Errors:**

**"SDK not found"**
```bash
# Set ANDROID_HOME environment variable
export ANDROID_HOME=/path/to/android/sdk
```

**"Gradle sync failed"**
```bash
# Refresh dependencies
./gradlew --refresh-dependencies assembleDebug
```

**"Build failed with compilation errors"**
- Check that all .kt files are in correct folders
- Verify package names match folder structure
- Ensure all imports are correct

**"Out of memory"**
```bash
# Increase Gradle memory in gradle.properties
org.gradle.jvmargs=-Xmx4096m
```

## 🎉 **Success!**

Once built successfully, you'll have:
- ✅ **app-debug.apk** ready for installation
- ✅ **~15-25 MB** optimized APK size
- ✅ **Full functionality** with EvolveNet demo
- ✅ **Ready for Samsung S25** testing

## 📞 **Need Help?**

If you encounter issues:
1. **Check error messages** carefully
2. **Verify all files** are present and correctly named
3. **Try clean build**: `./gradlew clean assembleDebug`
4. **Check Android Studio** for more detailed error info
5. **Ensure internet connection** for dependency downloads

**Your Free eSIM app APK will be ready in just a few minutes! 🚀**
