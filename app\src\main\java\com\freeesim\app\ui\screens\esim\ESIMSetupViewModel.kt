package com.freeesim.app.ui.screens.esim

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.freeesim.app.esim.ESIMManager
import com.freeesim.app.esim.ESIMDownloadResult
import com.freeesim.app.data.repository.UserRepository
import com.freeesim.app.data.repository.ESIMRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ESIMSetupViewModel @Inject constructor(
    private val esimManager: ESIMManager,
    private val userRepository: UserRepository,
    private val esimRepository: ESIMRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ESIMSetupUiState())
    val uiState: StateFlow<ESIMSetupUiState> = _uiState.asStateFlow()
    
    init {
        checkESIMCompatibility()
    }
    
    private fun checkESIMCompatibility() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val isSupported = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    esimManager.isESIMSupported()
                } else {
                    false
                }
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isESIMSupported = isSupported
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isESIMSupported = false,
                    setupStep = ESIMSetupStep.ERROR,
                    errorMessage = "Failed to check eSIM compatibility: ${e.message}"
                )
            }
        }
    }
    
    fun proceedToActivation() {
        _uiState.value = _uiState.value.copy(
            setupStep = ESIMSetupStep.ACTIVATION
        )
    }
    
    fun activateESIM() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)

            try {
                // Step 1: Purchase eSIM from provider (e.g., Telnyx)
                val purchaseResult = esimRepository.purchaseESIM(
                    country = "US", // Could be detected from user location
                    dataLimitMB = 1000L // 1GB initial data
                )

                purchaseResult.fold(
                    onSuccess = { profile ->
                        // Step 2: Activate the eSIM using Android's native API
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                            when (val result = esimManager.downloadSubscription(profile.activationCode)) {
                                is ESIMDownloadResult.Success -> {
                                    // Step 3: Mark as activated in our system
                                    esimRepository.activateESIM(profile.iccid)

                                    // Update user status
                                    userRepository.updateESIMStatus(
                                        isActivated = true,
                                        iccid = profile.iccid
                                    )

                                    _uiState.value = _uiState.value.copy(
                                        isLoading = false,
                                        setupStep = ESIMSetupStep.SUCCESS
                                    )
                                }
                                is ESIMDownloadResult.Error -> {
                                    _uiState.value = _uiState.value.copy(
                                        isLoading = false,
                                        setupStep = ESIMSetupStep.ERROR,
                                        errorMessage = "Failed to install eSIM: ${result.message}"
                                    )
                                }
                            }
                        } else {
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                setupStep = ESIMSetupStep.ERROR,
                                errorMessage = "eSIM requires Android 9 or higher"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            setupStep = ESIMSetupStep.ERROR,
                            errorMessage = "Failed to purchase eSIM: ${error.message}"
                        )
                    }
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    setupStep = ESIMSetupStep.ERROR,
                    errorMessage = "Activation failed: ${e.message}"
                )
            }
        }
    }
    
    fun retrySetup() {
        _uiState.value = _uiState.value.copy(
            setupStep = ESIMSetupStep.CHECKING_COMPATIBILITY,
            errorMessage = null
        )
        checkESIMCompatibility()
    }
}

data class ESIMSetupUiState(
    val setupStep: ESIMSetupStep = ESIMSetupStep.CHECKING_COMPATIBILITY,
    val isLoading: Boolean = false,
    val isESIMSupported: Boolean? = null,
    val errorMessage: String? = null
)

enum class ESIMSetupStep {
    CHECKING_COMPATIBILITY,
    ACTIVATION,
    SUCCESS,
    ERROR
}
