package com.freeesim.app.data.api

import kotlinx.coroutines.delay
import java.util.UUID

/**
 * Demo eSIM provider for testing without real API calls
 * This provides a FULLY FUNCTIONAL simulation of eSIM operations
 * Perfect for development, testing, and demonstrations
 */
class DemoESIMProvider : ESIMProviderInterface {

    private val demoProfiles = mutableMapOf<String, DemoESIMData>()
    private val usageSimulator = DataUsageSimulator()

    data class DemoESIMData(
        val esimId: String,
        val iccid: String,
        val activationCode: String,
        var status: String,
        var dataLimitMB: Long,
        var dataUsedMB: Long,
        val createdAt: Long = System.currentTimeMillis(),
        var activatedAt: Long? = null,
        var lastUsageUpdate: Long = System.currentTimeMillis()
    )

    /**
     * Simulates realistic data usage patterns
     */
    class DataUsageSimulator {
        private val random = kotlin.random.Random

        fun simulateUsage(profile: DemoESIMData): Long {
            val timeSinceLastUpdate = System.currentTimeMillis() - profile.lastUsageUpdate
            val hoursSinceUpdate = timeSinceLastUpdate / (1000 * 60 * 60)

            if (hoursSinceUpdate < 1) return profile.dataUsedMB

            // Simulate realistic usage: 1-10 MB per hour when active
            val newUsage = if (profile.status == "active") {
                val usagePerHour = random.nextLong(1, 11) // 1-10 MB per hour
                val additionalUsage = (hoursSinceUpdate * usagePerHour).toLong()
                (profile.dataUsedMB + additionalUsage).coerceAtMost(profile.dataLimitMB)
            } else {
                profile.dataUsedMB
            }

            profile.dataUsedMB = newUsage
            profile.lastUsageUpdate = System.currentTimeMillis()
            return newUsage
        }

        fun generateUsageHistory(profile: DemoESIMData): List<UsageRecord> {
            val history = mutableListOf<UsageRecord>()
            val now = System.currentTimeMillis()
            val activatedTime = profile.activatedAt ?: profile.createdAt

            // Generate hourly usage records since activation
            var currentTime = activatedTime
            var cumulativeUsage = 0L

            while (currentTime < now && cumulativeUsage < profile.dataUsedMB) {
                val hourlyUsage = random.nextLong(1, 15) // 1-15 MB per session
                cumulativeUsage = (cumulativeUsage + hourlyUsage).coerceAtMost(profile.dataUsedMB)

                history.add(
                    UsageRecord(
                        timestamp = java.time.Instant.ofEpochMilli(currentTime).toString(),
                        dataMB = hourlyUsage,
                        country = getRandomCountry(),
                        network = getRandomNetwork()
                    )
                )

                currentTime += random.nextLong(1, 6) * 60 * 60 * 1000 // 1-6 hours later
            }

            return history.takeLast(20) // Return last 20 records
        }

        private fun getRandomCountry(): String {
            val countries = listOf("US", "CA", "GB", "DE", "FR", "IT", "ES", "AU", "JP", "KR")
            return countries.random()
        }

        private fun getRandomNetwork(): String {
            val networks = listOf(
                "Verizon", "AT&T", "T-Mobile", "Vodafone", "Orange", "Deutsche Telekom",
                "Telefonica", "Telstra", "NTT DoCoMo", "KT Corporation"
            )
            return networks.random()
        }
    }
    
    override suspend fun purchaseESIM(request: PurchaseESIMRequest): Result<PurchaseESIMResponse> {
        // Simulate realistic API delay
        delay(2000)

        return try {
            val esimId = UUID.randomUUID().toString()
            val iccid = generateRealisticICCID()
            val activationCode = generateRealisticActivationCode()

            val demoData = DemoESIMData(
                esimId = esimId,
                iccid = iccid,
                activationCode = activationCode,
                status = "ready_to_activate",
                dataLimitMB = request.dataLimitMB,
                dataUsedMB = 0L
            )

            demoProfiles[esimId] = demoData

            // Simulate successful purchase
            Result.success(
                PurchaseESIMResponse(
                    esimId = esimId,
                    iccid = iccid,
                    activationCode = activationCode,
                    qrCode = "https://demo.freeesim.app/qr/$esimId",
                    status = "ready_to_activate",
                    dataLimitMB = request.dataLimitMB,
                    validUntil = "2025-12-31T23:59:59Z",
                    cost = 0.0, // Free for demo
                    currency = "USD"
                )
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun generateRealisticICCID(): String {
        // Generate realistic ICCID: 89 (telecom) + 01 (country) + 4103 (issuer) + random digits
        val random = kotlin.random.Random
        val randomDigits = (1..12).map { random.nextInt(0, 10) }.joinToString("")
        return "89014103$randomDigits"
    }

    private fun generateRealisticActivationCode(): String {
        val random = kotlin.random.Random
        val randomCode = (1..32).map {
            "0123456789ABCDEF"[random.nextInt(16)]
        }.joinToString("")
        return "LPA:1\$demo.freeesim.app\$$randomCode"
    }
    
    override suspend fun getESIMDetails(esimId: String): Result<ESIMDetailsResponse> {
        delay(1000)

        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))

        // Update usage with realistic simulation
        usageSimulator.simulateUsage(demoData)

        return Result.success(
            ESIMDetailsResponse(
                esimId = demoData.esimId,
                iccid = demoData.iccid,
                status = demoData.status,
                dataLimitMB = demoData.dataLimitMB,
                dataUsedMB = demoData.dataUsedMB,
                dataRemainingMB = demoData.dataLimitMB - demoData.dataUsedMB,
                validUntil = "2025-12-31T23:59:59Z",
                activatedAt = demoData.activatedAt?.let {
                    java.time.Instant.ofEpochMilli(it).toString()
                },
                networks = generateRealisticNetworks()
            )
        )
    }

    private fun generateRealisticNetworks(): List<NetworkInfo> {
        return listOf(
            NetworkInfo("United States", "US", "Verizon Wireless", "311480"),
            NetworkInfo("United States", "US", "AT&T Mobility", "310410"),
            NetworkInfo("United States", "US", "T-Mobile USA", "310260"),
            NetworkInfo("Canada", "CA", "Rogers Wireless", "302720"),
            NetworkInfo("Canada", "CA", "Bell Mobility", "302610"),
            NetworkInfo("United Kingdom", "GB", "Vodafone UK", "23415"),
            NetworkInfo("Germany", "DE", "Deutsche Telekom", "26201"),
            NetworkInfo("France", "FR", "Orange France", "20801"),
            NetworkInfo("Australia", "AU", "Telstra", "50501"),
            NetworkInfo("Japan", "JP", "NTT DoCoMo", "44010")
        )
    }
    
    override suspend fun activateESIM(esimId: String, request: ActivateESIMRequest): Result<ActivateESIMResponse> {
        // Simulate realistic activation time
        delay(3000)

        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))

        // Simulate potential activation failure (5% chance)
        if (kotlin.random.Random.nextFloat() < 0.05f) {
            return Result.success(
                ActivateESIMResponse(
                    success = false,
                    message = "Activation failed: Network temporarily unavailable. Please try again.",
                    activatedAt = null
                )
            )
        }

        // Update status to active with timestamp
        val activationTime = System.currentTimeMillis()
        demoData.status = "active"
        demoData.activatedAt = activationTime

        return Result.success(
            ActivateESIMResponse(
                success = true,
                message = "eSIM activated successfully! You can now use mobile data, calls, and SMS.",
                activatedAt = java.time.Instant.ofEpochMilli(activationTime).toString()
            )
        )
    }
    
    override suspend fun getDataUsage(esimId: String): Result<DataUsageResponse> {
        delay(500)

        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))

        // Update usage with realistic simulation
        usageSimulator.simulateUsage(demoData)

        // Generate realistic usage history
        val usageHistory = usageSimulator.generateUsageHistory(demoData)

        return Result.success(
            DataUsageResponse(
                esimId = esimId,
                totalDataMB = demoData.dataLimitMB,
                usedDataMB = demoData.dataUsedMB,
                remainingDataMB = demoData.dataLimitMB - demoData.dataUsedMB,
                usageHistory = usageHistory
            )
        )
    }
    
    override suspend fun addDataCredits(esimId: String, request: TopUpRequest): Result<TopUpResponse> {
        delay(1500)

        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))

        // Simulate potential top-up failure (2% chance)
        if (kotlin.random.Random.nextFloat() < 0.02f) {
            return Result.success(
                TopUpResponse(
                    success = false,
                    newDataLimitMB = demoData.dataLimitMB,
                    cost = 0.0,
                    currency = "USD"
                )
            )
        }

        // Add data to existing limit
        val newDataLimit = demoData.dataLimitMB + request.dataMB
        demoData.dataLimitMB = newDataLimit

        return Result.success(
            TopUpResponse(
                success = true,
                newDataLimitMB = newDataLimit,
                cost = 0.0, // Free for demo
                currency = "USD"
            )
        )
    }

    /**
     * Additional demo functions for enhanced realism
     */

    fun getActiveProfilesCount(): Int = demoProfiles.values.count { it.status == "active" }

    fun getTotalDataUsed(): Long = demoProfiles.values.sumOf { it.dataUsedMB }

    fun getTotalDataAllocated(): Long = demoProfiles.values.sumOf { it.dataLimitMB }

    fun simulateNetworkIssue(): Boolean {
        // 1% chance of network issues
        return kotlin.random.Random.nextFloat() < 0.01f
    }

    fun getProviderStats(): Map<String, Any> {
        return mapOf(
            "total_profiles" to demoProfiles.size,
            "active_profiles" to getActiveProfilesCount(),
            "total_data_used_mb" to getTotalDataUsed(),
            "total_data_allocated_mb" to getTotalDataAllocated(),
            "provider_name" to "Demo eSIM Provider",
            "provider_version" to "1.0.0",
            "last_updated" to System.currentTimeMillis()
        )
    }
}
