package com.freeesim.app.data.api

import kotlinx.coroutines.delay
import java.util.UUID

/**
 * Demo eSIM provider for testing without real API calls
 * This allows developers to test the app functionality without API keys
 */
class DemoESIMProvider : ESIMProviderInterface {
    
    private val demoProfiles = mutableMapOf<String, DemoESIMData>()
    
    data class DemoESIMData(
        val esimId: String,
        val iccid: String,
        val activationCode: String,
        val status: String,
        val dataLimitMB: Long,
        val dataUsedMB: Long,
        val createdAt: Long = System.currentTimeMillis()
    )
    
    override suspend fun purchaseESIM(request: PurchaseESIMRequest): Result<PurchaseESIMResponse> {
        // Simulate API delay
        delay(2000)
        
        return try {
            val esimId = UUID.randomUUID().toString()
            val iccid = "89014103211118510720" // Demo ICCID
            val activationCode = "LPA:1\$demo.provider.com\$${UUID.randomUUID()}"
            
            val demoData = DemoESIMData(
                esimId = esimId,
                iccid = iccid,
                activationCode = activationCode,
                status = "ready_to_activate",
                dataLimitMB = request.dataLimitMB,
                dataUsedMB = 0L
            )
            
            demoProfiles[esimId] = demoData
            
            Result.success(
                PurchaseESIMResponse(
                    esimId = esimId,
                    iccid = iccid,
                    activationCode = activationCode,
                    qrCode = "https://demo.provider.com/qr/$esimId",
                    status = "ready_to_activate",
                    dataLimitMB = request.dataLimitMB,
                    validUntil = "2024-12-31T23:59:59Z",
                    cost = 0.0, // Free for demo
                    currency = "USD"
                )
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getESIMDetails(esimId: String): Result<ESIMDetailsResponse> {
        delay(1000)
        
        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))
        
        return Result.success(
            ESIMDetailsResponse(
                esimId = demoData.esimId,
                iccid = demoData.iccid,
                status = demoData.status,
                dataLimitMB = demoData.dataLimitMB,
                dataUsedMB = demoData.dataUsedMB,
                dataRemainingMB = demoData.dataLimitMB - demoData.dataUsedMB,
                validUntil = "2024-12-31T23:59:59Z",
                activatedAt = if (demoData.status == "active") "2024-01-01T00:00:00Z" else null,
                networks = listOf(
                    NetworkInfo("United States", "US", "Demo Network", "310260"),
                    NetworkInfo("Canada", "CA", "Demo Network CA", "302720")
                )
            )
        )
    }
    
    override suspend fun activateESIM(esimId: String, request: ActivateESIMRequest): Result<ActivateESIMResponse> {
        delay(3000) // Simulate activation time
        
        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))
        
        // Update status to active
        demoProfiles[esimId] = demoData.copy(status = "active")
        
        return Result.success(
            ActivateESIMResponse(
                success = true,
                message = "eSIM activated successfully",
                activatedAt = "2024-01-01T00:00:00Z"
            )
        )
    }
    
    override suspend fun getDataUsage(esimId: String): Result<DataUsageResponse> {
        delay(500)
        
        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))
        
        // Simulate some usage data
        val usageHistory = listOf(
            UsageRecord(
                timestamp = "2024-01-01T10:00:00Z",
                dataMB = 50L,
                country = "US",
                network = "Demo Network"
            ),
            UsageRecord(
                timestamp = "2024-01-01T14:30:00Z",
                dataMB = 25L,
                country = "US",
                network = "Demo Network"
            )
        )
        
        val totalUsed = usageHistory.sumOf { it.dataMB }
        
        return Result.success(
            DataUsageResponse(
                esimId = esimId,
                totalDataMB = demoData.dataLimitMB,
                usedDataMB = totalUsed,
                remainingDataMB = demoData.dataLimitMB - totalUsed,
                usageHistory = usageHistory
            )
        )
    }
    
    override suspend fun addDataCredits(esimId: String, request: TopUpRequest): Result<TopUpResponse> {
        delay(1500)
        
        val demoData = demoProfiles[esimId]
            ?: return Result.failure(Exception("eSIM not found"))
        
        val newDataLimit = demoData.dataLimitMB + request.dataMB
        demoProfiles[esimId] = demoData.copy(dataLimitMB = newDataLimit)
        
        return Result.success(
            TopUpResponse(
                success = true,
                newDataLimitMB = newDataLimit,
                cost = 0.0, // Free for demo
                currency = "USD"
            )
        )
    }
}
