package com.freeesim.app

import com.freeesim.app.data.api.DemoESIMProvider
import com.freeesim.app.data.api.PurchaseESIMRequest
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit test for DemoESIMProvider to verify core functionality
 */
class DemoESIMProviderTest {
    
    private val demoProvider = DemoESIMProvider()
    
    @Test
    fun testPurchaseESIM() = runBlocking {
        val request = PurchaseESIMRequest(
            country = "US",
            dataLimitMB = 1000L
        )
        
        val result = demoProvider.purchaseESIM(request)
        
        assertTrue("Purchase should succeed", result.isSuccess)
        
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        assertEquals("Data limit should match", 1000L, response?.dataLimitMB)
        assertEquals("Status should be ready", "ready_to_activate", response?.status)
        assertTrue("ICCID should not be empty", !response?.iccid.isNullOrEmpty())
        assertTrue("Activation code should not be empty", !response?.activationCode.isNullOrEmpty())
    }
    
    @Test
    fun testActivateESIM() = runBlocking {
        // First purchase an eSIM
        val purchaseRequest = PurchaseESIMRequest(dataLimitMB = 500L)
        val purchaseResult = demoProvider.purchaseESIM(purchaseRequest)
        val esimId = purchaseResult.getOrNull()?.esimId
        assertNotNull("eSIM ID should not be null", esimId)
        
        // Then activate it
        val activateResult = demoProvider.activateESIM(esimId!!, com.freeesim.app.data.api.ActivateESIMRequest())
        
        assertTrue("Activation should succeed", activateResult.isSuccess)
        
        val response = activateResult.getOrNull()
        assertTrue("Activation should be successful", response?.success == true)
        assertNotNull("Activated timestamp should be set", response?.activatedAt)
    }
    
    @Test
    fun testGetESIMDetails() = runBlocking {
        // Purchase and activate an eSIM first
        val purchaseRequest = PurchaseESIMRequest(dataLimitMB = 2000L)
        val purchaseResult = demoProvider.purchaseESIM(purchaseRequest)
        val esimId = purchaseResult.getOrNull()?.esimId!!
        
        demoProvider.activateESIM(esimId, com.freeesim.app.data.api.ActivateESIMRequest())
        
        // Get details
        val detailsResult = demoProvider.getESIMDetails(esimId)
        
        assertTrue("Getting details should succeed", detailsResult.isSuccess)
        
        val details = detailsResult.getOrNull()
        assertEquals("Data limit should match", 2000L, details?.dataLimitMB)
        assertEquals("Status should be active", "active", details?.status)
        assertTrue("Should have network info", details?.networks?.isNotEmpty() == true)
    }
    
    @Test
    fun testDataUsage() = runBlocking {
        // Purchase an eSIM
        val purchaseRequest = PurchaseESIMRequest(dataLimitMB = 1000L)
        val purchaseResult = demoProvider.purchaseESIM(purchaseRequest)
        val esimId = purchaseResult.getOrNull()?.esimId!!
        
        // Get usage data
        val usageResult = demoProvider.getDataUsage(esimId)
        
        assertTrue("Getting usage should succeed", usageResult.isSuccess)
        
        val usage = usageResult.getOrNull()
        assertNotNull("Usage data should not be null", usage)
        assertTrue("Should have usage history", usage?.usageHistory?.isNotEmpty() == true)
        assertTrue("Used data should be positive", (usage?.usedDataMB ?: 0) > 0)
    }
    
    @Test
    fun testTopUp() = runBlocking {
        // Purchase an eSIM
        val purchaseRequest = PurchaseESIMRequest(dataLimitMB = 500L)
        val purchaseResult = demoProvider.purchaseESIM(purchaseRequest)
        val esimId = purchaseResult.getOrNull()?.esimId!!
        
        // Top up with additional data
        val topUpRequest = com.freeesim.app.data.api.TopUpRequest(dataMB = 300L)
        val topUpResult = demoProvider.addDataCredits(esimId, topUpRequest)
        
        assertTrue("Top up should succeed", topUpResult.isSuccess)
        
        val response = topUpResult.getOrNull()
        assertTrue("Top up should be successful", response?.success == true)
        assertEquals("New data limit should be 800MB", 800L, response?.newDataLimitMB)
    }
}
