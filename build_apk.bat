@echo off
REM Free eSIM App - APK Build Script for Windows
REM This script helps you build the APK from the project files

echo 🚀 Building Free eSIM App APK...
echo ==================================

REM Check if we're in the right directory
if not exist "settings.gradle.kts" (
    echo ❌ Error: Please run this script from the project root directory
    echo    Make sure you have all the project files in the current directory
    pause
    exit /b 1
)

REM Check if gradlew exists
if not exist "gradlew.bat" (
    echo ❌ Error: gradlew.bat not found. Please ensure all project files are present.
    pause
    exit /b 1
)

REM Clean previous builds
echo 🧹 Cleaning previous builds...
call gradlew.bat clean

REM Build the APK
echo 🔨 Building APK...
call gradlew.bat assembleDebug

REM Check if build was successful
if %ERRORLEVEL% EQU 0 (
    echo.
    echo 🎉 SUCCESS! APK built successfully!
    echo ==================================
    echo 📱 APK Location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 📋 Next Steps:
    echo 1. Copy the APK to your Samsung Galaxy S25
    echo 2. Enable 'Install unknown apps' in Settings
    echo 3. Install the APK and enjoy your Free eSIM app!
    echo.
    echo 🔧 To install via ADB:
    echo    adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
) else (
    echo.
    echo ❌ Build failed. Please check the error messages above.
    echo 💡 Common solutions:
    echo 1. Make sure you have Android SDK installed
    echo 2. Check that all project files are present
    echo 3. Ensure you have internet connection for dependencies
    echo 4. Try running: gradlew.bat --refresh-dependencies assembleDebug
)

pause
