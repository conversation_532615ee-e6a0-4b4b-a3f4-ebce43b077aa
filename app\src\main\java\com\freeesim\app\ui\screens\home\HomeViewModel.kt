package com.freeesim.app.ui.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.freeesim.app.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState: StateFlow<HomeUiState> = _uiState.asStateFlow()
    
    init {
        loadUserData()
    }
    
    private fun loadUserData() {
        viewModelScope.launch {
            userRepository.getCurrentUser().collect { user ->
                _uiState.value = _uiState.value.copy(
                    dataCredits = user?.dataCredits ?: 0L,
                    isESIMActivated = user?.isESIMActivated ?: false,
                    totalDataEarned = user?.totalDataEarned ?: 0L,
                    totalDataUsed = user?.totalDataUsed ?: 0L
                )
            }
        }
    }
}

data class HomeUiState(
    val dataCredits: Long = 0L,
    val isESIMActivated: Boolean = false,
    val totalDataEarned: Long = 0L,
    val totalDataUsed: Long = 0L,
    val isLoading: Boolean = false
)
